{"bsonType": "object", "required": ["user_id", "task_type", "target_type", "target_id", "execute_time", "status"], "properties": {"_id": {"description": "定时任务ID"}, "user_id": {"bsonType": "string", "description": "用户ID，关联用户表"}, "task_type": {"bsonType": "string", "description": "任务类型：on_shelf(定时上架)、off_shelf(定时下架)", "enum": ["on_shelf", "off_shelf"]}, "target_type": {"bsonType": "string", "description": "目标类型：account(账号级别)、shelf(货架级别)", "enum": ["account", "shelf"]}, "target_id": {"bsonType": "string", "description": "目标ID：游戏账号名(account类型)或货架ID(shelf类型)"}, "platform_type": {"bsonType": "string", "description": "平台类型标识（shelf类型时必填）"}, "execute_time": {"bsonType": "timestamp", "description": "计划执行时间戳"}, "relative_time_config": {"bsonType": "object", "description": "相对时间配置", "properties": {"days": {"bsonType": "int", "description": "天数", "minimum": 0, "default": 0}, "hours": {"bsonType": "int", "description": "小时数", "minimum": 0, "maximum": 23, "default": 0}, "minutes": {"bsonType": "int", "description": "分钟数", "minimum": 0, "maximum": 59, "default": 0}}}, "status": {"bsonType": "string", "description": "任务状态：pending(待执行)、completed(已完成)、failed(执行失败)、cancelled(已取消)", "enum": ["pending", "completed", "failed", "cancelled"], "default": "pending"}, "auto_disable_monitor": {"bsonType": "bool", "description": "定时下架时是否自动禁用监控状态", "default": true}, "execution_result": {"bsonType": "object", "description": "执行结果详情", "properties": {"success_count": {"bsonType": "int", "description": "成功操作数量", "default": 0}, "failed_count": {"bsonType": "int", "description": "失败操作数量", "default": 0}, "error_message": {"bsonType": "string", "description": "错误信息"}, "affected_shelves": {"bsonType": "array", "description": "受影响的货架列表", "items": {"bsonType": "object", "properties": {"shelf_id": {"bsonType": "string", "description": "货架ID"}, "platform_type": {"bsonType": "string", "description": "平台类型"}, "success": {"bsonType": "bool", "description": "操作是否成功"}, "message": {"bsonType": "string", "description": "操作结果消息"}}}}}}, "retry_count": {"bsonType": "int", "description": "重试次数", "default": 0, "maximum": 3}, "next_retry_time": {"bsonType": "timestamp", "description": "下次重试时间"}, "created_by": {"bsonType": "string", "description": "创建方式：manual(手动创建)、system(系统创建)", "enum": ["manual", "system"], "default": "manual"}, "description": {"bsonType": "string", "description": "任务描述"}, "create_time": {"bsonType": "timestamp", "description": "创建时间", "forceDefaultValue": {"$env": "now"}}, "update_time": {"bsonType": "timestamp", "description": "更新时间", "forceDefaultValue": {"$env": "now"}}, "execute_start_time": {"bsonType": "timestamp", "description": "开始执行时间"}, "execute_end_time": {"bsonType": "timestamp", "description": "执行完成时间"}}}