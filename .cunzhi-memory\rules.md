# 开发规范和规则

- testPlatformLogin云函数存在重复数据库更新问题：适配器层和云函数层都会更新数据库，且使用user_id+platform_type条件会误更新多个同类型配置。修复方案：使用配置ID精确更新，移除重复逻辑。
- shelf-monitor云函数修复：定时任务需要获取所有用户的平台配置，不能传入null作为用户ID。新增DatabaseManager.getAllPlatformConfigs()方法专门用于定时任务获取所有启用的平台配置。
- syncShelves云函数已完成重构：实现了基于game_account的去重机制、删除检测、性能优化（减少数据库查询、批量操作、性能监控），解决了重复账号数据和孤立数据问题
- shelf-monitor云函数数据同步问题修复：联动下架操作成功后必须立即更新本地数据库状态。在handleOffShelfOtherPlatforms函数中，调用adapter.offShelf()成功后需要立即调用updateShelfStatus()更新本地货架状态为OFFLINE(-1)，确保数据库状态实时同步。
- shelf-monitor云函数U号租平台权限错误修复：问题根因是自动重新登录成功后原始请求没有重新执行。修复方案：在base-adapter.js的executeRequest方法中，当检测到登录过期并重新登录成功后，使用新token重新执行原始请求，确保API调用使用最新的认证信息。
- 定时任务页面create_time字段问题修复：问题根因是后端创建任务时未显式设置create_time字段，导致页面访问task.create_time时为undefined。修复方案：1)在ScheduledTaskManager.createTask()中显式设置create_time和update_time字段；2)在getUserTasks()查询中添加容错处理，为历史记录使用update_time作为create_time的fallback值；3)确保返回数据始终包含create_time字段。
- 用户要求从定时任务系统中完全移除"执行中"(executing)状态，简化任务执行流程为：pending → completed/failed/cancelled，需要修改后端常量、前端页面、数据库schema和相关业务逻辑。
- 定时任务系统executing状态移除完成：1)后端ScheduledTaskManager移除EXECUTING常量和相关处理逻辑；2)shelf-monitor云函数移除设置执行中状态的代码；3)前端页面移除执行中状态筛选、文本处理和CSS样式；4)数据库schema移除executing枚举值。任务执行流程简化为pending→completed/failed/cancelled，保持重试和取消功能完整性。
- 用户要求在定时任务页面新增删除功能：支持删除所有状态的任务，物理删除数据库记录，前端需要确认对话框和加载状态，后端需要新增deleteTask方法和deleteScheduledTask操作，确保安全性和用户体验。
- 定时任务页面显示格式简化：移除账号级别任务中的监控状态信息（"未启用监控"、"监控中"等），统一显示格式为"账号：xxx | 平台：平台名(x个货架)"，保持货架级别任务显示格式不变。
