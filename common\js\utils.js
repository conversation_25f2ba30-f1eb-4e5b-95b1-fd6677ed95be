/**
 * 公共工具函数集合
 * {{ AURA-X: Modify - 重构为实用的工具函数集合，消除重复代码. Approval: 寸止(ID:1735374000). }}
 */

// {{ AURA-X: Add - 添加常用常量，避免硬编码. Approval: 寸止(ID:1735374000). }}
const CONSTANTS = {
  // 分页相关
  DEFAULT_PAGE_SIZE: 5,
  DEFAULT_CURRENT_PAGE: 1,

  // 表单验证
  MIN_PASSWORD_LENGTH: 6,
  MOBILE_LENGTH: 11
}

class Utils {
  /**
   * 格式化时间显示
   * @param {number|string|Date} timestamp 时间戳或时间字符串
   * @returns {string} 格式化后的时间文本
   */
  formatTime(timestamp) {
    if (!timestamp) return '从未'

    // {{ AURA-X: Modify - 修复时间格式化逻辑，支持多种时间格式包括ISO 8601. Approval: 寸止(ID:1735374800). }}
    let date

    // 处理不同的时间格式
    if (typeof timestamp === 'string') {
      // 检查是否为ISO 8601格式 (如: 2025-08-24T11:32:04.492Z)
      if (timestamp.includes('T') || timestamp.includes('-')) {
        date = new Date(timestamp)
      } else {
        // 纯数字字符串，当作时间戳处理
        let numTimestamp = parseInt(timestamp)
        if (numTimestamp.toString().length === 10) {
          numTimestamp = numTimestamp * 1000
        }
        date = new Date(numTimestamp)
      }
    } else if (typeof timestamp === 'number') {
      // 数字时间戳：10位为秒，13位为毫秒
      let numTimestamp = timestamp
      if (numTimestamp.toString().length === 10) {
        numTimestamp = numTimestamp * 1000
      }
      date = new Date(numTimestamp)
    } else {
      // Date对象或其他类型
      date = new Date(timestamp)
    }

    const now = new Date()

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return '时间格式错误'
    }

    const diff = now.getTime() - date.getTime()

    // 处理未来时间
    if (diff < 0) {
      const futureDiff = Math.abs(diff)
      if (futureDiff < 60000) {
        return '即将执行'
      } else if (futureDiff < 3600000) {
        return Math.floor(futureDiff / 60000) + '分钟后'
      } else if (futureDiff < 86400000) {
        return Math.floor(futureDiff / 3600000) + '小时后'
      } else {
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})
      }
    }

    // 处理过去时间
    if (diff < 60000) {
      return '刚刚'
    } else if (diff < 3600000) {
      return Math.floor(diff / 60000) + '分钟前'
    } else if (diff < 86400000) {
      return Math.floor(diff / 3600000) + '小时前'
    } else if (diff < 2592000000) { // 30天内
      return Math.floor(diff / 86400000) + '天前'
    } else {
      // 超过30天显示具体日期和时间
      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})
    }
  }

  /**
   * 获取平台名称
   * @param {string} platformType 平台类型
   * @returns {string} 平台显示名称
   */
  getPlatformName(platformType) {
    const platformMap = {
      'zuhaowan': '租号玩',
      'uhaozu': 'U号租'
    }
    return platformMap[platformType] || platformType
  }

  /**
   * 获取操作动作文本
   * @param {string} action 操作动作
   * @returns {string} 动作显示文本
   */
  getActionText(action) {
    const actionMap = {
      'login': '登录',
      'sync': '更新',
      'on_shelf': '上架',
      'off_shelf': '下架',
      'enable_monitor': '开启监控',
      'disable_monitor': '关闭监控'
    }
    return actionMap[action] || action
  }

  /**
   * 显示成功提示
   * @param {string} title 提示文本
   * @param {number} duration 显示时长
   */
  showSuccess(title, duration = 1500) {
    uni.showToast({
      title,
      icon: 'success',
      duration
    })
  }

  /**
   * 显示错误提示
   * @param {string} title 提示文本
   * @param {number} duration 显示时长
   */
  showError(title, duration = 2000) {
    uni.showToast({
      title,
      icon: 'none',
      duration
    })
  }

  /**
   * 显示加载提示
   * @param {string} title 提示文本
   */
  showLoading(title = '加载中...') {
    uni.showLoading({
      title,
      mask: true
    })
  }

  /**
   * 隐藏加载提示
   */
  hideLoading() {
    uni.hideLoading()
  }

  /**
   * 显示详情弹窗
   * @param {string} title 标题
   * @param {string} content 内容
   * @param {boolean} showCancel 是否显示取消按钮
   */
  showDetail(title, content, showCancel = false) {
    uni.showModal({
      title,
      content,
      showCancel
    })
  }

  /**
   * 防抖函数
   * @param {Function} func 要防抖的函数
   * @param {number} delay 延迟时间
   * @returns {Function} 防抖后的函数
   */
  debounce(func, delay = 300) {
    let timeoutId
    return function (...args) {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => func.apply(this, args), delay)
    }
  }

  /**
   * 深拷贝对象
   * @param {any} obj 要拷贝的对象
   * @returns {any} 拷贝后的对象
   */
  deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj
    if (obj instanceof Date) return new Date(obj.getTime())
    if (obj instanceof Array) return obj.map(item => this.deepClone(item))
    if (typeof obj === 'object') {
      const clonedObj = {}
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = this.deepClone(obj[key])
        }
      }
      return clonedObj
    }
  }

  /**
   * 验证手机号格式
   * @param {string} mobile 手机号
   * @returns {boolean} 是否有效
   */
  validateMobile(mobile) {
    const mobileReg = /^1[3-9]\d{9}$/
    return mobileReg.test(mobile) && mobile.length === CONSTANTS.MOBILE_LENGTH
  }

  /**
   * 获取常量值
   * @param {string} key 常量键名
   * @returns {any} 常量值
   */
  getConstant(key) {
    return CONSTANTS[key]
  }

  /**
   * 获取所有常量
   * @returns {Object} 常量对象
   */
  getConstants() {
    return { ...CONSTANTS }
  }

  /**
   * 格式化价格显示
   * @param {number} price 价格
   * @param {string} unit 单位
   * @returns {string} 格式化后的价格
   */
  formatPrice(price, unit = '元') {
    if (typeof price !== 'number') return '0' + unit
    return price.toFixed(2) + unit
  }
}

const utils = new Utils()
export default utils